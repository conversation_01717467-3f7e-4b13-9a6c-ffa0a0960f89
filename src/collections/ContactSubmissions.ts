import type { CollectionConfig } from 'payload';

export const ContactSubmissions: CollectionConfig = {
  slug: 'contact-submissions',
  access: {
    create: () => true, // allow public creation
    read: ({ req }) => <PERSON><PERSON><PERSON>(req.user), // only admin (logged in) can read
    update: ({ req }) => <PERSON><PERSON><PERSON>(req.user),
    delete: ({ req }) => <PERSON><PERSON><PERSON>(req.user),
  },
  admin: {
    useAsTitle: 'email',
    defaultColumns: ['name', 'email', 'subject', 'createdAt'],
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'email',
      type: 'email',
      required: true,
    },
    {
      name: 'subject',
      type: 'text',
      required: true,
    },
    {
      name: 'message',
      type: 'textarea',
      required: true,
    },
    {
      name: 'consent',
      type: 'checkbox',
      required: true,
      defaultValue: false,
      label: 'Wyrażam zgodę na przetwarzanie moich danych osobowych w celu kontaktu.'
    },
  ],
  timestamps: true,
};

export default ContactSubmissions; 