"use client";
import Image from "next/image";
import Link from "next/link";

export default function ContactSuccessPage() {
  return (
    <div className="min-h-screen flex flex-col justify-between bg-white relative overflow-hidden">
      <div className="flex flex-col max-w-7xl mx-auto w-full py-16 px-4 md:px-8 lg:px-20 gap-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-8">
          <div className="flex-1 flex flex-col justify-center z-10">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold font-['Montserrat',Helvetica] text-[#114438] mb-6 leading-tight">
              Dziękuję za wypełnienie<br />formularza kontaktowego
            </h1>
            <p className="text-xl md:text-2xl text-[#114438] mb-8 font-['Montserrat',Helvetica]">
              Skontaktuję się z Tobą tak szybko jak to możliwe!
            </p>
            <div className="mb-8">
              <Image src="/podpis.png" alt="Podpis" width={260} height={80} />
            </div>
            <Link href="/" className="inline-block">
              <button className="rounded-full px-8 py-4 bg-gradient-to-r from-[#1ca9e2] to-[#73b737] text-white font-bold font-['Montserrat',Helvetica] text-lg shadow-lg transition hover:opacity-90">
                WRÓĆ NA STRONĘ GŁÓWNĄ
              </button>
            </Link>
          </div>
        </div>
      </div>
      <Image
        src="/contact-success.png"
        alt="Contact Success Illustration"
        width={420}
        height={420}
        className="absolute right-0 bottom-0 w-[260px] md:w-[340px] lg:w-[420px] h-auto z-0 select-none pointer-events-none"
        priority
      />
      {/* Footer bar (reuse from main site) */}
      <footer className="bg-gradient-to-r from-green-400 to-blue-400 text-white py-4 px-4 flex flex-col md:flex-row justify-between items-center text-xs mt-8">
        <div>
          © Katarzyna Tyszkiewicz 2025 <Link href="/polityka-prywatnosci" className="underline ml-2">Polityka Prywatności</Link>
        </div>
        <div>
          Dumnie wspierane przez <a href="https://moondew.net" target="_blank" rel="noopener noreferrer" className="underline">Moondew.net</a>.
        </div>
        <div className="flex gap-3 mt-2 md:mt-0">
          <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" aria-label="Facebook">
            <svg width="24" height="24" fill="currentColor" className="text-white"><path d="M22 12c0-5.522-4.477-10-10-10S2 6.478 2 12c0 4.991 3.657 9.128 8.438 9.877v-6.987h-2.54v-2.89h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.242 0-1.632.771-1.632 1.562v1.875h2.773l-.443 2.89h-2.33v6.987C18.343 21.128 22 16.991 22 12z"/></svg>
          </a>
          <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" aria-label="Instagram">
            <svg width="24" height="24" fill="currentColor" className="text-white"><path d="M7.75 2h8.5A5.75 5.75 0 0 1 22 7.75v8.5A5.75 5.75 0 0 1 16.25 22h-8.5A5.75 5.75 0 0 1 2 16.25v-8.5A5.75 5.75 0 0 1 7.75 2zm0 1.5A4.25 4.25 0 0 0 3.5 7.75v8.5A4.25 4.25 0 0 0 7.75 20.5h8.5A4.25 4.25 0 0 0 20.5 16.25v-8.5A4.25 4.25 0 0 0 16.25 3.5h-8.5zm4.25 3.25a5.25 5.25 0 1 1 0 10.5 5.25 5.25 0 0 1 0-10.5zm0 1.5a3.75 3.75 0 1 0 0 7.5 3.75 3.75 0 0 0 0-7.5zm6 1.25a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/></svg>
          </a>
        </div>
      </footer>
    </div>
  );
} 