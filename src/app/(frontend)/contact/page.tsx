"use client";
import Image from 'next/image';
import { useState } from 'react';
import { useRouter } from 'next/navigation';

// Blob SVG as a React component
function BlobDecoration({ className = "" }: { className?: string }) {
  return (
    <svg width="341" height="431" viewBox="0 0 341 431" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
      <g opacity="0.33">
        <path d="M15.627 218.229C8.63912 248.607 1.14124 279.184 0.686056 310.352C0.230872 341.523 7.4843 373.872 27.1162 398.077C46.7481 422.282 80.3517 436.447 110.191 427.473C130.742 421.291 147.163 405.279 158.508 387.058C176.202 358.648 183.152 323.715 177.681 290.697C175.186 275.638 169.496 260.014 157.071 251.154C144.646 242.295 124.256 243.792 117.129 257.29C113.121 264.885 114.082 274.2 116.821 282.339C122.14 298.14 133.617 311.398 147.323 320.882C182.587 345.289 231.106 344.568 270.184 326.912C300.804 313.076 327.883 288.091 337.071 255.755C341.943 238.605 341.602 220.345 339.052 202.698C334.913 174.043 324.608 145.562 305.162 124.128C285.715 102.689 256.2 89.2787 227.599 93.6601C216.586 95.3469 205.696 99.7705 197.949 107.783C190.203 115.795 186.077 127.733 189.061 138.474C192.04 149.214 202.977 157.703 214.024 156.265C230.019 154.182 237.592 135.176 238.431 119.063C240.871 72.0779 210.715 25.1602 166.976 7.89178C123.236 -9.37659 69.1828 4.29051 38.8962 40.2779C8.60963 76.2653 4.35281 131.887 28.8105 172.07L15.6228 218.233L15.627 218.229Z" fill="#66CAFF" fillOpacity="0.8" />
      </g>
    </svg>
  );
}

export default function ContactPage() {
  const [form, setForm] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    consent: false,
  });
  const [submitted, setSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox' && e.target instanceof HTMLInputElement) {
      setForm((prev) => ({
        ...prev,
        [name]: (e.target as HTMLInputElement).checked,
      }));
    } else {
      setForm((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    if (!form.name || !form.email || !form.subject || !form.message) {
      setError('Wszystkie pola są wymagane.');
      return;
    }
    if (!form.consent) {
      setError('Musisz wyrazić zgodę na przetwarzanie danych.');
      return;
    }
    setIsLoading(true);

    try {
      const response = await fetch('/contact/api', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(form),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setSubmitted(true);
        router.push('/contact/success');
      } else {
        setError(result.error || 'Wystąpił błąd podczas wysyłania wiadomości.');
      }
    } catch (err) {
      setError('Wystąpił błąd podczas wysyłania wiadomości.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="relative flex flex-col justify-between bg-white">
      {/* Blobs - background decorations */}
      <BlobDecoration className="pointer-events-none z-0 absolute left-0 top-0 w-[340px] h-[430px] rotate-12 opacity-60" />
      <BlobDecoration className="pointer-events-none z-0 absolute right-0 bottom-0 w-[220px] h-[278px] -rotate-45 opacity-60" />

      <div className="flex flex-col-reverse xl:flex-row max-w-7xl mx-auto w-full py-12 px-4 md:px-8 gap-8 xl:gap-12 relative z-10">
        {/* Left: Illustration + Info side by side */}
        <div className="flex-1 flex flex-row items-center xl:items-start justify-center gap-6 mb-8 xl:mb-0">
          <div className="flex-shrink-0 hidden md:block">
            <Image src="/standing-illustration-2.png" alt="Standing Illustration" width={220} height={350} />
          </div>
          <div className="text-gray-800 text-lg md:text-base max-w-xl font-['Montserrat',Helvetica]">
            <p className="mb-4">
              Jestem doświadczonym lektorem dzieci, młodzieży i dorosłych. Zawsze kieruję się <b>złotą zasadą – indywidualne podejście</b>. Języka angielskiego uczę się od 4. roku życia. Ciągle i nieprzerwanie z pasją, bo sprawia mi to niezwykłą przyjemność. <b>Nauka języka to systematyczna praca</b>, ale trzeba to zrobić w sposób, który daje poczucie, że jest to interesujące, ważne i potrzebne.
            </p>
            <p className="mb-4">
              Zapisz się na moje zajęcia i przekonaj się, że <b>uczenie się języka angielskiego może być fajne!</b> Zachęcam do skorzystania z darmowych materiałów do druku, dzięki którym nauka języka będzie jeszcze ciekawsza!
            </p>
            {/* <p className="mb-4">
              P.S. <b>Relaks jest ważny</b>, bo od samej nauki można zwariować. Trzeba też coś jeść i zwiedzać. W czasie wolnym zajrzyj do zakładki <Link href="/po-godzinach" className="underline font-semibold">Po godzinach</Link>.
            </p> */}
            <p className="mb-4">Enjoy!</p>
            <p className="mb-4">Dziękuję, że odwiedzasz moją stronę, a w razie wszelkich pytań <b>zapraszam do kontaktu poprzez formularz.</b></p>
            <div className="mt-6 max-w-[320px] w-full block">
              <Image src="/podpis.png" alt="Podpis" width={320} height={100} />
            </div>
          </div>
        </div>
        {/* Right: Form */}
        <div className={`flex-1 flex flex-col ${typeof window !== 'undefined' && window.innerWidth < 1280 ? 'mt-0' : ''}`}>
          <div className={`rounded-[32px] p-[3px] bg-gradient-to-r from-[#73b737] to-[#1ca9e2] w-full max-w-xl mx-auto ${typeof window !== 'undefined' && window.innerWidth < 1280 ? 'mt-0' : 'mt-0 xl:mt-0'}`}>
            <form onSubmit={handleSubmit} className="font-['Montserrat',Helvetica] bg-white/90 rounded-[28px] shadow-lg p-8 flex flex-col gap-4 w-full" style={{boxShadow: '0 4px 32px 0 rgba(44, 183, 55, 0.08)'}}>
              <h2 className="text-3xl font-bold mb-2 text-center">FORMULARZ KONTAKTOWY</h2>
              <div className="flex flex-col gap-4">
                <div className="rounded-[20px] p-[2px] bg-gradient-to-r from-[#73b737] to-[#1ca9e2]">
                  <input
                    type="text"
                    name="name"
                    placeholder="Imię i Nazwisko:"
                    value={form.name}
                    onChange={handleChange}
                    className="w-full rounded-[18px] px-4 py-2 font-['Montserrat',Helvetica] text-base border-none outline-none bg-white focus:bg-white"
                    required
                  />
                </div>
                <div className="rounded-[20px] p-[2px] bg-gradient-to-r from-[#73b737] to-[#1ca9e2]">
                  <input
                    type="email"
                    name="email"
                    placeholder="Adres e-mail:"
                    value={form.email}
                    onChange={handleChange}
                    className="w-full rounded-[18px] px-4 py-2 font-['Montserrat',Helvetica] text-base border-none outline-none bg-white focus:bg-white"
                    required
                  />
                </div>
                <div className="rounded-[20px] p-[2px] bg-gradient-to-r from-[#73b737] to-[#1ca9e2]">
                  <input
                    type="text"
                    name="subject"
                    placeholder="Temat wiadomości:"
                    value={form.subject}
                    onChange={handleChange}
                    className="w-full rounded-[18px] px-4 py-2 font-['Montserrat',Helvetica] text-base border-none outline-none bg-white focus:bg-white"
                    required
                  />
                </div>
                <div className="rounded-[20px] pt-[2px] px-[2px] bg-gradient-to-r from-[#73b737] to-[#1ca9e2]">
                  <textarea
                    name="message"
                    placeholder="Treść wiadomości:"
                    value={form.message}
                    onChange={handleChange}
                    className="w-full rounded-[18px] px-4 py-2 font-['Montserrat',Helvetica] text-base border-none outline-none bg-white focus:bg-white min-h-[120px]"
                    required
                  />
                </div>
              </div>
              <div className="flex items-start gap-2 mb-2 mt-2">
                <input
                  type="checkbox"
                  name="consent"
                  id="consent"
                  checked={form.consent}
                  onChange={handleChange}
                  className="mt-1 w-5 h-5 rounded-[6px] border-2 border-[#73b737] accent-[#73b737]"
                  required
                />
                <label htmlFor="consent" className="text-xs text-gray-700 font-['Montserrat',Helvetica]">
                  Wyrażam zgodę na przetwarzanie moich danych osobowych, w celu kontaktu udzielenia odpowiedzi na wiadomość przesłaną za pośrednictwem formularza kontaktowego.
                </label>
              </div>
              {error && <div className="text-red-500 text-sm mb-2">{error}</div>}
              {submitted ? (
                <div className="text-green-600 text-center font-semibold">Dziękujemy za wiadomość!</div>
              ) : (
                <button
                  type="submit"
                  className="flex items-center justify-center gap-2 h-14 px-8 py-4 rounded-full bg-gradient-to-r from-[#1ca9e2] to-[#73b737] font-['Montserrat',Helvetica] font-bold text-white text-lg shadow-lg transition w-full mt-2"
                  style={{letterSpacing: '0.5px'}}
                  disabled={isLoading}
                >
                  WYŚLIJ WIADOMOŚĆ
                  {isLoading ? (
                    <svg width="28" height="28" fill="none" viewBox="0 0 28 28"><path d="M19.5 14H4.5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M14 8.5L19.5 14L14 19.5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  ) : (
                    <svg width="28" height="28" fill="none" viewBox="0 0 28 28"><path d="M19.5 14H4.5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M14 8.5L19.5 14L14 19.5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  )}
                </button>
              )}
            </form>
          </div>
        </div>
      </div>
    </div>
  );
} 