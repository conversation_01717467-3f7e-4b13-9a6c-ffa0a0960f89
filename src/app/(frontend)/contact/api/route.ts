import { NextRequest, NextResponse } from 'next/server';
import { type } from 'arktype';
import payload from 'payload';

const ContactSchema = type({
  name: 'string > 0',
  email: 'string.email',
  subject: 'string > 0',
  message: 'string > 0',
  consent: 'boolean = true',
});

export async function POST(req: NextRequest) {
  try {
    const data = await req.json();
    const result = ContactSchema(data);
    if (result instanceof type.errors) {
      return NextResponse.json({ error: result.summary }, { status: 400 });
    }


    await payload.create({
      collection: 'contact-submissions',
      data: {
        ...result,
        consent: result.consent ? true : false,
      },
    });
    return NextResponse.redirect('/contact/success');
  } catch (err) {
    console.error('Error in POST request:', err);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
} 