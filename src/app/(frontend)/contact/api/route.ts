import { NextRequest, NextResponse } from 'next/server';
import { type } from 'arktype';
import { getPayload } from 'payload';
import configPromise from '@payload-config';

const ContactSchema = type({
  name: 'string > 0',
  email: 'string.email',
  subject: 'string > 0',
  message: 'string > 0',
  consent: 'boolean = true',
});

export async function POST(req: NextRequest) {
  try {
    const data = await req.json();
    console.log('Received data:', data);

    const result = ContactSchema(data);
    if (result instanceof type.errors) {
      console.log('Validation errors:', result.summary);
      return NextResponse.json({ error: result.summary }, { status: 400 });
    }

    console.log('Validated data:', result);
    const payload = await getPayload({ config: configPromise });
    console.log('Payload instance created');

    const submission = await payload.create({
      collection: 'contact-submissions',
      data: {
        name: result.name,
        email: result.email,
        subject: result.subject,
        message: result.message,
        consent: <PERSON><PERSON><PERSON>(result.consent),
      },
    });

    console.log('Contact submission created:', submission.id);
    return NextResponse.json({ success: true, message: 'Wiadomość została wysłana pomyślnie!' });
  } catch (err) {
    console.error('Error in POST request:', err);
    return NextResponse.json({ error: 'Wystąpił błąd serwera. Spróbuj ponownie.' }, { status: 500 });
  }
}