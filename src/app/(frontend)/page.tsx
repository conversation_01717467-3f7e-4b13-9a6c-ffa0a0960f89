import { generateMetadata } from './[slug]/page'

import { HeroSection } from "@/components/LanguageLearning/HeroSection";
import { AboutSection } from "@/components/LanguageLearning/AboutSection";
import { ServicesSection } from "@/components/LanguageLearning/ServicesSection";
import { PricingSection } from "@/components/LanguageLearning/PricingSection";
import { TestimonialsSection } from "@/components/LanguageLearning/TestimonialsSection";
import ScrollToTopButton from "@/components/LanguageLearning/ScrollToTopButton";

export default function Home() {
  return (
    <div className="bg-white min-h-screen overflow-x-hidden">
      <div className="pointer-events-none absolute top-0 right-0 z-10">
        <svg width="235" height="217" viewBox="0 0 235 217" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M22.6367 -56C17.3263 -48.2003 13.3434 -39.368 10.2825 -30.4251C-5.46438 15.525 1.04461 69.7357 31.5059 107.591C61.9671 145.428 116.934 163.295 162.294 145.926C178.889 139.564 194.23 128.446 202.454 112.68C207.248 103.498 209.406 92.4341 205.976 82.6615C202.546 72.8888 192.571 65.1075 182.282 66.3429C174.648 67.2649 168.231 72.7044 163.88 79.0474C153.019 94.8497 153.019 116.534 161.022 133.959C169.024 151.384 184.089 164.807 200.887 174.045C232.528 191.489 271.49 195.066 305.786 183.689C335.141 173.953 360.698 153.357 376.832 127.007" stroke="url(#paint0_linear_1_50)" strokeWidth="2.8" strokeLinecap="round" strokeLinejoin="round"/>
          <defs>
            <linearGradient id="paint0_linear_1_50" x1="189.416" y1="-56" x2="189.416" y2="190.242" gradientUnits="userSpaceOnUse">
              <stop stopColor="#73B737"/>
              <stop offset="1" stopColor="#335118"/>
            </linearGradient>
          </defs>
        </svg>
      </div>
      <div className="pointer-events-none absolute -top-6 left-1/2 translate-x-[10%] z-10 w-[600px] max-w-none" style={{minWidth: '400px'}}>
        <svg width="613" height="324" viewBox="0 0 613 324" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g opacity="0.33">
            <path d="M45.9358 -11.1076C6.95684 43.5493 -9.88723 114.921 6.54655 180.03C22.9803 245.117 74.4627 301.631 139.442 318.518C153.781 322.254 168.746 324.111 183.474 322.47C227.355 317.59 261.411 283.556 293.047 252.74C324.662 221.924 362.691 190.46 406.788 192.879C428.426 194.066 448.812 203.503 470.32 206.116C524.308 212.681 576.417 173.162 597.709 123.105C619.002 73.0696 614.618 15.5406 600.581 -37L45.9574 -11.1292L45.9358 -11.1076Z" fill="#66CAFF" fillOpacity="0.6"/>
          </g>
        </svg>
      </div>

      <section id="home">
        <HeroSection />
      </section>
      <div className="w-full max-w-7xl mx-auto">
        <section id="about" className="px-4 md:px-8 lg:px-20">
          <AboutSection />
        </section>
        <section id="oferta-zajec" className="px-4 md:px-8 lg:px-20">
          <ServicesSection />
        </section>
        </div>
        <section id="cennik" className="px-4 md:px-8 lg:px-20">
          <PricingSection />
        </section>
        <div className="w-full max-w-7xl mx-auto">
        <section id="opinie">
          <TestimonialsSection />
        </section>
      </div>
      <ScrollToTopButton />
    </div>
  );
};


export { generateMetadata }
