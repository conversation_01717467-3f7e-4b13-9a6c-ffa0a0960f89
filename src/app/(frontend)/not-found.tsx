import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'

export default function NotFound() {
  return (
    <div className="flex flex-col justify-center max-w-7xl mx-auto bg-white px-4 py-8 sm:py-12 md:py-16 lg:py-32 md:px-8 lg:px-20">
      <div className="max-w-7xl w-full mx-auto flex flex-col lg:flex-row items-center justify-between gap-16 lg:gap-0">
        {/* Left: Text and Button */}
        <div className="flex-1 flex flex-col items-start justify-center text-left max-w-2xl w-full px-2">
          <h1 className="text-6xl sm:text-7xl md:text-8xl lg:text-8xl font-bold bg-gradient-to-r from-[#1CA9E2] to-[#73B737] bg-clip-text text-transparent font-montserrat mb-8 leading-tight">
            Error 404
          </h1>
          <div className="text-base sm:text-lg md:text-xl font-montserrat text-black space-y-4 mb-10">
            <p>Ups! Wygląda na to, że zgubiliśmy się pomiędzy podręcznikami do języka angielskiego..</p>
            <p>Niestety, strona, której szukasz, nie istnieje. Ale nie martw się! Jesteśmy tu, aby pomóc Ci znaleźć drogę powrotną.</p>
            <p>Szukasz idealnych lekcji języka angielskiego, które przynoszą satysfakcje?<br/>A może chciałbyś podszkolić swój język, ale nie wiesz od czego zacząć?</p>
            <p>Wróć na stronę główną i zacznij swoją przygodę razem ze mną!</p>
          </div>
          <Button
            asChild
            className="flex h-10 px-8 py-4 rounded-[20px] bg-[linear-gradient(97deg,rgba(28,169,226,1)_0%,rgba(115,183,55,1)_100%)] hover:bg-[linear-gradient(97deg,rgba(28,169,226,0.9)_0%,rgba(115,183,55,0.9)_100%)] font-['Montserrat',Helvetica] font-bold text-white text-base sm:text-lg shadow-lg transition"
          >
            <Link href="/">WRÓĆ NA STRONĘ GŁÓWNĄ</Link>
          </Button>
        </div>
        {/* Right: Illustration */}
        <div className="flex-1 flex items-center justify-center w-full max-w-lg px-2 mt-12 lg:mt-0">
          <Image
            src="/404.png"
            alt="404 Illustration"
            width={600}
            height={500}
            className="w-full h-auto max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl 2xl:max-w-2xl mx-auto"
            priority
          />
        </div>
      </div>
    </div>
  )
}
