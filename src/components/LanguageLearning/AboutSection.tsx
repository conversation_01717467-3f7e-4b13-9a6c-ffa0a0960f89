import React from "react";
import { Card, CardContent } from "@/components/ui/card";

export const AboutSection = (): React.JSX.Element => {
  return (
    <section className="w-full py-12 bg-white">
      <h2 className="font-['Montserrat',Helvetica] font-bold text-black text-[32px] mb-8">
        <PERSON> jeste<PERSON>?
      </h2>

      <div className="flex flex-col md:flex-row items-start gap-8">
        <div className="w-full md:w-[323px] aspect-square rounded-3xl overflow-hidden flex-shrink-0">
          <img
            className="w-full h-auto max-w-[323px] object-cover object-top"
            alt="Katarzyna Tysz<PERSON>wicz - nauczycielka języka angielskiego"
            src="/teacher-photo.png"
          />
        </div>

        <Card className="border-none shadow-none bg-white">
          <CardContent className="p-0 font-['Montserrat',Helvetica] text-base">
            <p className="text-black mb-4">
              Ukończyłam filologię angielską i prawo na UW. Mam duże
              doświadczenie jako lektor dzieci, młodzieży, studentów i
              dorosłych. Najbardziej lubię uczyć indywidualnie, tak, aby osoba
              mówiła swobodnie o rzeczach, które ją interesują i są dla niej czy
              dla niego ważne.{" "}
            </p>

            <p className="font-bold text-[#73b737] mb-4">
              Przygotowuję też do egzaminu ósmoklasisty, matur oraz certyfikatu
              First B2, C1 Advanced czy B2 UW
            </p>

            <p className="text-black mb-4">
              Ze swojej strony oferuję przede wszystkim cierpliwość,
              profesjonalizm, przyjazną atmosferę i entuzjastyczne podejście.
              Zajęcia są zawsze dobrze przygotowane (materiały w pakiecie) i
              interesujące. Lubię używać gier i materiałów wizualnych.
            </p>

            <p className="text-black">
              <span className="font-bold text-[#1ca9e2]">Co Cię czeka? -</span>
              <span className="text-black">
                {" "}
                Więcej niż dobra lekcja. Zero stresu i nudy! :) Niech nauka, a w
                szczególności mówienie, będzie przyjemnością!
              </span>
            </p>
            <img
              src="/podpis.png"
              alt="Podpis Katarzyna Tyszkiewicz"
              className="mt-6 max-w-[320px] w-full block"
            />
          </CardContent>
        </Card>
      </div>
    </section>
  );
};
