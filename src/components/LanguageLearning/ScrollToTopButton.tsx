"use client";
import React from "react";

const ScrollToTopButton = () => (
  <button
    className="fixed bottom-8 right-8 w-[52px] h-[52px] rounded-[5px] p-0 shadow-lg z-50 hover:scale-110 transition-transform bg-gradient-to-b from-[#73B737] to-[#1CA9E2] border border-white"
    onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
    aria-label="Scroll to top"
    type="button"
  >
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mx-auto my-auto">
      <path d="M12 19V5" stroke="white" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M6 11L12 5L18 11" stroke="white" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  </button>
);

export default ScrollToTopButton; 