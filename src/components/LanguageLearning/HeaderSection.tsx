"use client";
import React, { useState } from "react";
import Link from "next/link";

export const HeaderSection = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMobileMenuOpen(false);
  };
  const navItems = [
    { label: "Oferta zajęć", sectionId: "oferta-zajec" },
    { label: "Cennik", sectionId: "cennik" },
    { label: "Opinie", sectionId: "opinie" },
    { label: "Kontakt", href: "/contact" },
  ];

  const socialIcons = [
    {
      name: 'Instagram',
      href: 'https://www.instagram.com/',
      svg: (
        <svg width="32" height="31" viewBox="0 0 32 31" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clipPath="url(#clip0_4_324)">
            <path d="M16.0015 18.7276C17.8423 18.7276 19.3346 17.2819 19.3346 15.4986C19.3346 13.7153 17.8423 12.2696 16.0015 12.2696C14.1606 12.2696 12.6683 13.7153 12.6683 15.4986C12.6683 17.2819 14.1606 18.7276 16.0015 18.7276Z" fill="#1CA9E2"/>
            <path d="M20.5845 7.42612H11.4184C9.34972 7.42612 7.66858 9.05755 7.66858 11.0587V19.9413C7.66858 21.9425 9.34972 23.5739 11.4184 23.5739H20.5845C22.6532 23.5739 24.3343 21.9425 24.3343 19.9413V11.0587C24.3343 9.05755 22.6532 7.42612 20.5845 7.42612ZM16.0015 20.3449C13.2423 20.3449 11.0017 18.1715 11.0017 15.4986C11.0017 12.8256 13.2452 10.6551 16.0015 10.6551C18.7577 10.6551 21.0012 12.8285 21.0012 15.4986C21.0012 18.1687 18.7577 20.3449 16.0015 20.3449ZM21.3129 11.4624C20.6807 11.4624 20.1679 10.9656 20.1679 10.3531C20.1679 9.7406 20.6807 9.24384 21.3129 9.24384C21.9452 9.24384 22.458 9.7406 22.458 10.3531C22.458 10.9656 21.9452 11.4624 21.3129 11.4624Z" fill="#1CA9E2"/>
            <path d="M16.0015 0C7.16453 0 0 6.93781 0 15.4986C0 24.0594 7.16453 31 16.0015 31C24.8384 31 32 24.0594 32 15.4986C32 6.93781 24.8384 0 16.0015 0ZM26.0009 19.9413C26.0009 22.8344 23.571 25.1884 20.5845 25.1884H11.4184C8.42903 25.1884 5.99909 22.8344 5.99909 19.9384V11.0587C5.99909 8.16562 8.42903 5.81162 11.4184 5.81162H20.5845C23.571 5.81162 26.0009 8.16562 26.0009 11.0587V19.9413Z" fill="#1CA9E2"/>
          </g>
          <defs>
            <clipPath id="clip0_4_324">
              <rect width="32" height="31" fill="white"/>
            </clipPath>
          </defs>
        </svg>
      ),
    },
    {
      name: 'Facebook',
      href: 'https://www.facebook.com/',
      svg: (
        <svg width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clipPath="url(#clip0_1_16)">
            <path d="M31 15.5396C31 23.5769 24.9151 30.1906 17.1115 31V19.6714H21.1446L21.8078 15.6019H17.1115V12.5002C17.1115 11.2182 17.453 10.3069 19.2875 10.3069H21.9997V6.20623C21.9997 6.20623 20.2781 5.827 18.6638 5.827C15.294 5.827 13.0615 8.05989 13.0615 11.8464V15.6019H9.00027V19.6714H13.0615V30.8896C5.65869 29.718 0 23.291 0 15.5396C0 6.95618 6.94 0 15.5 0C24.06 0 31 6.95618 31 15.5396Z" fill="#1CA9E2"/>
          </g>
          <defs>
            <clipPath id="clip0_1_16">
              <rect width="31" height="31" fill="white"/>
            </clipPath>
          </defs>
        </svg>
      ),
    },
  ];

  return (
    <header className={`relative w-full max-w-7xl mx-auto ${isMobileMenuOpen ? 'bg-white z-[1000]' : 'z-10'}`}>
      <div className="flex flex-row items-center justify-between px-4 md:px-8 lg:px-20 py-6">
        <div className="flex-shrink-0">
          <Link href="/">
            <img
              className="w-20 h-auto cursor-pointer"
              onClick={() => scrollToSection('home')}
              alt="Angielski z Panią Kasią - Logo"
              src="/logo-main.png"
            />
          </Link>
        </div>
        <nav className="hidden lg:block">
          <ul className="flex items-center gap-6">
            {navItems.map((item, index) => (
              <li key={index}>
                {typeof item.href === 'string' ? (
                  <Link
                    href={item.href}
                    className="font-['Montserrat',Helvetica] font-normal text-black text-base hover:text-blue-600 transition-colors cursor-pointer"
                  >
                    {item.label}
                  </Link>
                ) : (
                  <Link
                    href={`/#${item.sectionId}`}
                    className="font-['Montserrat',Helvetica] font-normal text-black text-base hover:text-blue-600 transition-colors cursor-pointer"
                    onClick={(e) => {
                      if (window.location.pathname === "/") {
                        e.preventDefault();
                        scrollToSection(item.sectionId);
                      }
                    }}
                  >
                    {item.label}
                  </Link>
                )}
              </li>
            ))}
          </ul>
        </nav>
        <button
          className="lg:hidden p-2 rounded-md"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Toggle mobile menu"
        >
          <div className="w-6 h-6 flex flex-col justify-center space-y-1">
            <span className={`block h-0.5 w-6 bg-black transition-transform duration-300 ${isMobileMenuOpen ? 'rotate-45 translate-y-1.5' : ''}`}></span>
            <span className={`block h-0.5 w-6 bg-black transition-opacity duration-300 ${isMobileMenuOpen ? 'opacity-0' : ''}`}></span>
            <span className={`block h-0.5 w-6 bg-black transition-transform duration-300 ${isMobileMenuOpen ? '-rotate-45 -translate-y-1.5' : ''}`}></span>
          </div>
        </button>
        <div className="hidden lg:flex items-center gap-4">
          <div className="flex items-center gap-3">
            {socialIcons.map((icon) => (
              <a
                key={icon.name}
                href={icon.href}
                target="_blank"
                rel="noopener noreferrer"
                aria-label={icon.name}
                className="hover:opacity-80 transition-opacity cursor-pointer"
              >
                {icon.svg}
              </a>
            ))}
          </div>
        </div>
      </div>
      {isMobileMenuOpen && (
        <div className="sticky inset-0 bg-white z-[999] lg:hidden overflow-y-auto">
          <div className="px-4 py-6 space-y-6">
            {navItems.map((item, index) => (
              typeof item.href === 'string' ? (
                <Link
                  key={index}
                  href={item.href}
                  className="block font-['Montserrat',Helvetica] font-normal text-black text-lg hover:text-blue-600 transition-colors cursor-pointer"
                >
                  {item.label}
                </Link>
              ) : (
                <Link
                  key={index}
                  href={`/#${item.sectionId}`}
                  className="block font-['Montserrat',Helvetica] font-normal text-black text-lg hover:text-blue-600 transition-colors cursor-pointer"
                  onClick={(e) => {
                    if (window.location.pathname === "/") {
                      e.preventDefault();
                      scrollToSection(item.sectionId);
                    }
                  }}
                >
                  {item.label}
                </Link>
              )
            ))}
            <div className="py-4 border-y border-gray-200">
              <div className="flex items-center justify-center gap-4">
                {socialIcons.map((icon) => (
                  <a
                    key={icon.name}
                    href={icon.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label={icon.name}
                    className="hover:opacity-80 transition-opacity cursor-pointer"
                  >
                    {icon.svg}
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
}; 