import React from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

interface Feature {
  text: string | React.ReactElement;
  subtext?: string;
  isBold?: boolean;
}

export const ServicesSection = (): React.JSX.Element => {
  // Course offerings data
  const courseOfferings = [
    {
      id: 1,
      title: "Język angielski ogólny \noraz konwersacje",
      titleColor: "text-[#325118]",
      borderColor: "border-[#73b737]",
      imageSrc: "/standard-english.jpg",
      imagePosition: "left",
      buttonGradient: "bg-[linear-gradient(97deg,rgba(28,169,226,1)_0%,rgba(115,183,55,1)_100%)]",
      features: [
        {
          text: (
            <span className="font-bold">
              Ćwiczenie 4 najważniejszych sprawności: <br />
            </span>
          ),
          subtext: "mówienie, pisanie, czytanie i słuchanie",
        },
        { text: "Utrwalanie struktur gramatycznych i nauka słownictwa" },
        { text: "Praca nad wymową" },
        { text: "EFEKT: SWOBODA KOMUNIKACYJNA", isBold: true },
      ] as Feature[],
      hasCheckboxes: false,
    },
    {
      id: 2,
      title: "Język angielski \nbiznesowy i prawniczy",
      titleColor: "text-[#325118]",
      borderColor: "border-[#73b737]",
      imageSrc: "/business-english.jpg",
      imagePosition: "left",
      buttonGradient: "bg-[linear-gradient(97deg,rgba(28,169,226,1)_0%,rgba(115,183,55,1)_100%)]",
      features: [
        { text: "Korespondencja i rozmowy telefoniczne w biznesie" },
        { text: "Terminologia prawnicza i ekonomiczna" },
        { text: "Prezentacje i negocjacje biznesowe" },
        { text: "Umowy, kontrakty i dokumenty prawne" },
        { text: "Język angielski w środowisku korporacyjnym" },
      ] as Feature[],
      hasCheckboxes: true,
    },
    {
      id: 3,
      title: "Język angielski \ndla dzieci i młodzieży",
      titleColor: "text-[#325118]",
      borderColor: "border-[#73b737]",
      imageSrc: "/english-for-children.jpg",
      imagePosition: "left",
      buttonGradient: "bg-[linear-gradient(97deg,rgba(28,169,226,1)_0%,rgba(115,183,55,1)_100%)]",
      features: [
        { text: "Bieżąca pomoc w lekcjach i przygotowanie do sprawdzianów" },
        { text: "Język angielski funkcyjny i sytuacyjny" },
        { text: "Gry i zabawy językowe" },
        {
          text: "4 sprawności (mówienie, pisanie, czytanie i słuchanie) + wymowa",
        },
        { text: "Gramatyka i słownictwo" },
        { text: "Misja egzamin ósmoklasisty / Matura" },
      ] as Feature[],
      hasCheckboxes: true,
    },
    {
      id: 4,
      title: "Przygotowanie \ndo egzaminów",
      titleColor: "text-[#325118]",
      borderColor: "border-[#73b737]",
      imageSrc: "/exam-english.jpg",
      imagePosition: "left",
      buttonGradient: "bg-[linear-gradient(97deg,rgba(28,169,226,1)_0%,rgba(115,183,55,1)_100%)]",
      features: [
        { text: "Egzamin ósmoklasisty" },
        { text: "Matura podstawowa i rozszerzona" },
        { text: "Certyfikaty Cambridge (FCE, CAE, CPE)" },
        { text: "IELTS i TOEFL" },
        { text: "Egzaminy na studia (B2 UW)" },
      ] as Feature[],
      hasCheckboxes: true,
    },
    {
      id: 5,
      title: "Tłumaczenia",
      titleColor: "text-[#325118]",
      borderColor: "border-[#73b737]",
      imageSrc: "/translations.jpg",
      imagePosition: "left",
      buttonGradient: "bg-[linear-gradient(97deg,rgba(28,169,226,1)_0%,rgba(115,183,55,1)_100%)]",
      features: [
        { text: "Korespondencja" },
        { text: "CV i list motywacyjny" },
        { text: "Prezentacje, oferty, katalogi i instrukcje" },
        { text: "Strony internetowe" },
        {
          text: "Pisma z elementami terminologii prawniczej i ekonomicznej itp.",
        },
      ] as Feature[],
      hasCheckboxes: true,
    },
  ];

  return (
    <section className="w-full py-8 bg-white">
      <h2 className="font-bold font-['Montserrat',Helvetica] text-black text-[32px] mb-8">
        Oferta zajęć
      </h2>

      <div className="flex flex-col gap-8">
        {courseOfferings.map((course, idx) => {
          // Determine odd/even (0-based idx)
          const isEven = idx % 2 === 1;
          const borderColor = isEven ? 'border-[#1CA9E2]' : 'border-[#73B737]';
          const gradient = isEven
            ? 'bg-gradient-to-b from-[rgba(28,169,226,0.2)] to-white'
            : 'bg-gradient-to-b from-[rgba(115,183,55,0.2)] to-white';
          const flexDirection = isEven ? 'lg:flex-row-reverse' : 'lg:flex-row';
          const titleColor = isEven ? 'text-[#005393]' : 'text-[#325118]';
          const checkboxGradient = isEven
            ? 'bg-gradient-to-b from-[#1CA9E2] to-[#0F5D7C]'
            : 'bg-gradient-to-b from-[#73b737] to-[#325118]';
          return (
            <Card
              key={course.id}
              className={`w-full rounded-[20px] bg-white border-2 border-solid ${borderColor} overflow-hidden ${gradient}`}
            >
              <CardContent className={`p-0 relative flex flex-col ${flexDirection} h-full min-h-[400px]`}>
                <div className="w-full lg:w-[530px] flex-shrink-0 p-12 bg-transparent flex items-center justify-center">
                  <Image
                    className="w-full h-auto rounded-3xl"
                    alt="Course illustration"
                    src={course.imageSrc}
                    width={400}
                    height={300}
                  />
                </div>
                <div className="flex-1 p-8 lg:p-12 flex flex-col justify-between">
                  <div>
                    <h3
                      className={`font-['Montserrat',Helvetica] font-bold text-2xl lg:text-3xl ${titleColor} mb-6 whitespace-pre-line`}
                    >
                      {course.title}
                    </h3>
                    <div className="flex flex-col gap-3 mb-auto">
                      {course.features.map((feature, index) => (
                        <div key={index} className="flex items-start gap-3">
                          {course.hasCheckboxes && (
                            <div
                              className={`w-[17px] h-[17px] rounded-sm flex-shrink-0 mt-1.5 ${checkboxGradient}`}
                            />
                          )}
                          <p
                            className={`font-['Montserrat',Helvetica] ${feature.isBold ? "font-bold" : "font-normal"} text-black text-xl`}
                          >
                            {feature.text}
                            {feature.subtext && (
                              <span className="font-['Montserrat',Helvetica] font-normal text-black text-xl">
                                {feature.subtext}
                              </span>
                            )}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                 <Button
                   className={`mt-8 w-[207px] rounded-[20px] ${course.buttonGradient} text-white font-['Montserrat',Helvetica] font-bold hover:opacity-90`}
                 >
                   CENNIK
                 </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </section>
  );
};
