"use client";
import React from "react";
import { Button } from "@/components/ui/button";

export const HeroSection = (): React.JSX.Element => {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };
  return (
    <section className="relative w-full min-h-[calc(100dvh-300px)] bg-white">
      <div className="pointer-events-none absolute bottom-32 left-0 z-0 max-lg:hidden">
        <svg width="222" height="467" viewBox="0 0 222 467" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M-11.43 448.01C-1.62301 448.739 8.42415 447.731 18.0907 445.833C67.7694 436.102 113.306 401.978 131.6 354.758C149.877 307.548 137.361 248.626 98.0465 216.737C83.6572 205.074 65.6275 197.021 47.1128 197.814C36.3267 198.272 25.217 202.09 18.1837 210.278C11.1504 218.466 9.32546 231.524 15.8021 240.167C20.6121 246.577 28.8654 249.534 36.858 250.156C56.78 251.724 76.3515 240.424 87.9085 224.121C99.4655 207.819 103.731 187.227 103.332 167.242C102.588 129.594 85.5136 92.5644 57.3733 67.5379C33.2894 46.1165 1.38238 33.7827 -30.8071 32.9512" stroke="#73B737" strokeWidth="2.8" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </div>

      <img
        src="/hero.png"
        alt="Angielski z Panią Kasią - Hero"
        className="hidden xl:block pointer-events-none absolute right-0 top-56 h-auto max-h-[600px] w-auto z-0 object-contain"
        style={{ minWidth: 350 }}
      />

      <div className="relative z-10 max-w-7xl mx-auto">
        
        <div className="relative z-10 flex flex-col lg:flex-row items-center pl-4 md:pl-8 lg:pl-20 pb-12 max-lg:pt-24">
          <div className="w-full lg:w-1/2 lg:max-w-[80%] xl:max-w-none mb-12 lg:mb-0 text-center lg:text-left pr-4 md:pr-8 lg:pr-0 xl:pt-48">
            <h1 className="font-['Montserrat',Helvetica] max-lg:max-w-xl max-lg:mx-auto font-bold text-black text-3xl md:text-4xl lg:text-5xl leading-loose text-balance mb-8">
              Poznaj nowy wymiar
              nauki języka angielskiego
            </h1>

            <Button 
              className="px-8 py-4 rounded-[20px] bg-[linear-gradient(97deg,rgba(28,169,226,1)_0%,rgba(115,183,55,1)_100%)] hover:bg-[linear-gradient(97deg,rgba(28,169,226,0.9)_0%,rgba(115,183,55,0.9)_100%)] font-['Montserrat',Helvetica] font-bold text-white text-base"
              onClick={() => scrollToSection('about')}
            >
              DOWIEDZ SIĘ WIĘCEJ
            </Button>
          </div>

        <div className="w-full lg:w-1/2 flex justify-center lg:justify-end">
          <div className="relative w-full h-full flex items-end lg:items-center justify-end">
            <img
              className="block w-full max-w-none lg:w-auto lg:max-w-[700px] lg:mt-32 h-auto xl:hidden object-contain object-right"
              alt="Angielski z Panią Kasią - Hero"
              src="/hero.png"
              style={{ right: 0, bottom: 0 }}
            />
          </div>
          </div>
        </div>
      </div>
    </section>
  );
};
