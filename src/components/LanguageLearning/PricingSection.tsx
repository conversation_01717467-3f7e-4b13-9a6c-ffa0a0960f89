import React from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

export const PricingSection = (): React.JSX.Element => {
  // Define service cards data for mapping
  const serviceCards = [
    {
      title: "General \nEnglish",
      price: "150 PLN / 55 min",
      color: "#73b737",
      description: [
        "konwersacje dla d<PERSON>ci, młod<PERSON><PERSON>y",
        "i dorosłych",
        "",
        "bieżąca pomoc w nauce",
        "",
        "przygotowanie do egzaminów",
        "i certyfikatów",
      ],
    },
    {
      title: "Law / Business\nEnglish",
      price: "165 PLN / 55 min",
      color: "#1ca9e2",
      description: [
        "Korespondencja i rozmowy",
        "telefoniczne w biznesie",
        "",
        "Terminologia prawnicza",
        "i ekonomiczna",
        "",
        "Prezentacje i negocjacje",
        "biznesowe",
      ],
    },
    {
      title: "Konsultacje\nIndywidualne",
      price: "350 PLN / 165 min",
      color: "#1ca9e2",
      description: [
        "Pakiet 3 spotkań po 55 min",
        "",
        "przygotowanie CV i listu",
        "motywacyjnego",
        "",
        "wpisu w mediach społecznościowych",
        "",
        "rozmowa o pracę",
      ],
    },
    {
      title: "Tłumaczenia",
      price: "stawka ustalana \nindywidualnie",
      color: "#73b737",
      description: [
        "Korespondencja",
        "",
        "Prezentacje, oferty, katalogi",
        "i instrukcje",
        "",
        "Pisma z elementami terminologii",
        "prawniczej i ekonomicznej",
        "",
        "Strony internetowe",
      ],
    },
  ];

  return (
    <div className="relative">
      <section className="w-full py-16 bg-white max-w-7xl mx-auto px-4 md:px-8 lg:px-20 ">
        <h2 className="text-[32px] font-bold font-['Montserrat',Helvetica] text-black mb-8">
          Cennik
        </h2>

        {/* Service Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-6 mb-12">
          {serviceCards.map((card, index) => (
            <Card
              key={index}
              className="h-full rounded-[20px] border-2 border-solid hover:shadow-lg transition-shadow duration-300 bg-white"
              style={{ borderColor: card.color }}
            >
              <CardContent className="p-6 flex flex-col h-full">
                <h3
                  className="mt-4 mb-6 font-['Montserrat',Helvetica] font-bold text-[28px] lg:text-[32px] text-center tracking-[0] leading-normal whitespace-pre-line min-h-[72px]"
                  style={{ color: card.color }}
                >
                    {card.title}
                  </h3>

                <div 
                  className="mb-6 font-['Montserrat',Helvetica] font-semibold text-xl lg:text-2xl text-center tracking-[0] leading-normal whitespace-pre-line text-black min-h-[48px]"
                >
                    {card.price}
                </div>

                <div className="flex-grow mb-6 font-['Montserrat',Helvetica] font-normal text-black text-sm lg:text-base text-center tracking-[0] leading-[18px] min-h-[120px]">
                  {card.description.map((line, i) => (
                    <React.Fragment key={i}>
                        {line}
                      <br />
                    </React.Fragment>
                    ))}
                  </div>

                <Button className="w-full mt-auto rounded-[20px] bg-[linear-gradient(90deg,rgba(28,169,226,1)_0%,rgba(115,183,55,1)_100%)] hover:bg-[linear-gradient(90deg,rgba(28,169,226,0.9)_0%,rgba(115,183,55,0.9)_100%)] font-['Montserrat',Helvetica] font-bold text-white text-base min-h-[44px]">
                  KONTAKT
                    </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Info */}
        <div className="w-full max-w-4xl mx-auto font-['Montserrat',Helvetica] font-normal text-black text-base text-center tracking-[0] leading-normal px-4">
          <p className="mb-4">
            Istnieje możliwość wykupienia pakietu lekcji [5 po 55 min, 10 po 55 min].
          </p>
          <p>
            Prowadzę zajęcia z dofinansowaniem od firmy dla pracownika, wystawiam faktury.
          </p>
        </div>

      </section>
      <div className="pointer-events-none select-none w-screen absolute left-0 right-0 bottom-0 z-0" style={{ minHeight: 0 }}>
        <img
          src="/blob-decoration.svg"
          alt=""
          className="w-full h-auto object-cover"
          draggable="false"
        />
        {/* Gradient overlay */}
        <div
          className="absolute left-0 right-0 bottom-0 h-32 w-full"
          style={{
            background: "linear-gradient(to bottom, rgba(255,255,255,0) 0%, #fff 100%)",
            pointerEvents: "none",
          }}
          aria-hidden="true"
        />
      </div>
    </div>
  );
};
