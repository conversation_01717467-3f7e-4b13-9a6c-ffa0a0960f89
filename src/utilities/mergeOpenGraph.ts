import type { Metadata } from 'next'
import { getServerSideURL } from './getURL'

const defaultOpenGraph: Metadata['openGraph'] = {
  type: 'website',
  description: 'Profesjonalna nauka języka angielskiego z doświadczonym lektorem. Kursy dla dzieci, młodzieży i dorosłych. Język biznesowy, eg<PERSON>iny, tłumaczenia.',
  images: [
    {
      url: `${getServerSideURL()}/hero.png`,
    },
  ],
  siteName: 'Kasia - Nauka Języka Angielskiego',
  title: 'Kasia - Nauka Języka Angielskiego',
}

export const mergeOpenGraph = (og?: Metadata['openGraph']): Metadata['openGraph'] => {
  return {
    ...defaultOpenGraph,
    ...og,
    images: og?.images ? og.images : defaultOpenGraph.images,
  }
}
